---
title: PEEK-A-BOO
description: PEEK-A-BOO is an interactive installation blending art and tech — combining Max/MSP, Arduino, projection, and p5.js shaders to create a real-time audiovisual space. It delivers an immersive sensory experience that sparks creativity and emotional connection.
image: /projects/peekaboo/peekaboo.jpg
url: "peekaboo"
tags: ["Installation Art", "Creative Coding"]
date: 2023-08-29
client: Goldsmiths, London
responsibilities: ["Programming Development"]
honors: []
---

> PEEK-A-BOO is an installation art project that blends art and technology. By integrating various technical methods, including Max/MSP sound processing, Arduino hardware control, projection technology, and p5.js shader programming, it creates a creative and interactive experiential space. The project aims to provide an immersive sensory experience through real-time generation and processing of graphics and sound, inspiring creativity and emotional resonance in the audience.

![thumbnail](/projects/peekaboo/peekaboo.jpg "thumbnail")

## Project Story

In a space filled with a technological and artistic atmosphere, the PEEK-A-BOO project takes the audience into a dynamically changing world. By interacting with the environment, the audience can see real-time generated graphics and hear unique sound effects, as if entering a dreamlike space where technology and art intertwine.

The project’s inspiration comes from the everyday game of “peek-a-boo,” symbolizing the joy of exploration and discovery. Each time the audience moves or touches the installation, the system generates unique graphics and sound feedback based on their actions, making every moment full of surprises and the unknown. It feels like a game of “peek-a-boo” with the installation, exploring the endless possibilities of art and technology.

![thumbnail](/projects/peekaboo/p2.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p3.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p4.jpg "thumbnail")

## Technical Challenges
1. Real-time Sound and Graphics Generation: This required complex sound processing and generation using Max/MSP, combined with p5.js shader programming to achieve real-time graphics generation and changes. The system needed efficient computational power and optimized algorithms to ensure real-time performance and a smooth experience.
2. Multi-technology Integration: The project involved multiple technologies, including Max/MSP, Arduino, projection technology, and p5.js. These technologies needed to be seamlessly integrated to ensure real-time data transmission and processing while maintaining system stability and reliability.
3. Interactive Design: Designing interaction methods that allow every movement of the audience to effectively interact with the system, generating unique sound and graphic feedback. It required precise capture and analysis of audience behavior to design intuitive and engaging interaction modes.
4. Hardware Compatibility and Debugging: The use of Arduino hardware required careful debugging and programming to ensure accurate sensor data collection and response speed. At the same time, the calibration of projection equipment was crucial to ensure image clarity and synchronization.

![thumbnail](/projects/peekaboo/p5.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p6.jpg "thumbnail")

## Creative Inspiration
PEEK-A-BOO is not just an installation art project; it is also a platform for inspiring creative ideas. Through interaction with the system, the audience can experience the endless possibilities brought about by the fusion of technology and art. This immersive experience can spark curiosity and creativity, encouraging the audience to explore and try new ways of creative expression.

![thumbnail](/projects/peekaboo/p7.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p8.jpg "thumbnail")
![thumbnail](/projects/peekaboo/p9.jpg "thumbnail")