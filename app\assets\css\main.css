@import 'tailwindcss';
@import '@nuxt/ui';

@source "../../../content/**/*";

@theme static {
  --font-sans: 'Public Sans', sans-serif;
  --font-serif: 'Instrument Serif', serif;
}

html,
body {
  font-size: large;
}

:root {
  --ui-container: var(--container-4xl);

  ::selection {
    color: #282a30;
    background-color: #c8c8c8;
  }
}

.dark {
  ::selection {
    color: #ffffff;
    background-color: #474747;
  }
}

/* Marquee animation */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes marquee-rtl {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}
