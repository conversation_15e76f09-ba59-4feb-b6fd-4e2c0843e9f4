---
title: GAC Toyota - Vilandara
description: I built an interactive 3D web app with Three.js for GAC Toyota’s 2020 Venza, allowing users to explore the car in real time — from changing colors to opening doors and simulating operation. This immersive experience boosted customer engagement and elevated the brand’s digital presence.
image: /projects/guangqifengtian-car-3d-show/intro.png
url: "guangqi-car-3d-show"
tags: ["Web3D", "Font-end"]
date: 2020-01-07
client: Guangqi Toyota Motor Co., Ltd.
responsibilities: ["Front-end Development"]
honors: []
---

## Demo video
::YoutubePlayer{videoId="lri0MMf5RYI"}
:: 

## Project Introduction
I developed a 3D web application using the Three.js framework for GAC Toyota to showcase their 2020 model, the Venza. The project presents an interactive 3D model of the Venza, allowing users to switch vehicle colors, open the doors and windows, turn on the headlights, and simulate the car's operation. This innovative digital display provides potential customers with a more intuitive and immersive experience, enhancing the brand's image and improving customer engagement.